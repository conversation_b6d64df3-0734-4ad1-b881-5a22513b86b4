.smtcmp-chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: calc(var(--size-4-1) * -1);
}

.smtcmp-chat-header-title {
  font-size: var(--font-ui-medium);
  font-weight: var(--font-medium);
  margin: 0;
}

.smtcmp-chat-header-buttons {
  display: flex;
  gap: var(--size-2-1);
}

.smtcmp-markdown {
  line-height: var(--line-height-normal);
  font-size: var(--font-ui-small);

  h1 {
    font-size: var(--font-ui-large);
  }

  h2 {
    font-size: var(--font-ui-medium);
  }

  h3 {
    font-size: var(--font-ui-small);
  }

  h4 {
    font-size: var(--font-ui-smaller);
  }

  h5 {
    font-size: var(--font-ui-smallest);
  }

  h6 {
    font-size: var(--font-ui-smallest);
  }

  p {
    font-size: var(--font-ui-small);
  }

  ul {
    font-size: var(--font-ui-small);
    padding-left: var(--size-4-4);
  }

  ol {
    font-size: var(--font-ui-small);
    padding-left: var(--size-4-4);
  }

  li {
    font-size: var(--font-ui-small);
  }

  blockquote {
    font-size: var(--font-ui-small);
    font-style: var(--blockquote-style);
    background-color: var(--blockquote-background-color);
    margin: 0;
    padding-left: var(--size-4-2);
    border-left: var(--blockquote-border-thickness) solid
      var(--blockquote-border-color);
  }

  code {
    font-size: var(--font-ui-small);
    border-radius: var(--code-radius);
    padding: 0.1em 0.25em;
    color: var(--code-normal);
    font-size: var(--code-size);
    background-color: var(--code-background);
    vertical-align: baseline;
  }

  table {
    font-size: var(--font-ui-small);
  }

  thead {
    font-size: var(--font-ui-small);
  }

  tbody {
    font-size: var(--font-ui-small);
  }

  tr {
    font-size: var(--font-ui-small);
  }

  td {
    font-size: var(--font-ui-small);
  }

  th {
    font-size: var(--font-ui-small);
  }
}

button:not(.clickable-icon).smtcmp-chat-list-dropdown {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 26px;
  height: 26px;
  padding: 0;
  background-color: transparent;
  border-color: transparent;
  box-shadow: none;
  color: var(--text-muted);

  &:hover {
    background-color: var(--background-modifier-hover);
  }
}

.smtcmp-chat-container {
  display: flex;
  position: relative;
  flex-direction: column;
  height: 100%;

  .smtcmp-stop-gen-btn {
    z-index: 1000;
    position: absolute;
    bottom: 160px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: var(--size-4-1);
  }
}

.smtcmp-chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  user-select: text;
  display: flex;
  flex-direction: column;
  gap: var(--size-4-1);
  padding: 0 var(--size-4-3) var(--size-4-5) var(--size-4-3);
  margin: var(--size-4-2) calc(var(--size-4-3) * -1) 0;

  .smtcmp-chat-messages-user {
    display: flex;
    flex-direction: column;
    gap: var(--size-4-1);
  }

  .smtcmp-chat-messages-assistant {
    display: flex;
    flex-direction: column;
    padding-bottom: var(--size-4-2);
  }
}

.obsidian-default-textarea {
  -webkit-app-region: no-drag;
  background: var(--background-modifier-form-field);
  color: var(--text-normal);
  font-family: inherit;
  padding: 0;
  font-size: var(--font-ui-small);
  outline: none;
  min-height: 80px;
  max-height: 200px;
  overflow-y: auto;
  font-size: var(--font-ui-small);
  padding: var(--size-2-1);
}

@media (hover: hover) {
  .obsidian-default-textarea:hover {
    border-color: var(--background-modifier-border-hover);
    transition:
      box-shadow 0.15s ease-in-out,
      border 0.15s ease-in-out;
  }
}
.obsidian-default-textarea:active {
  border-color: var(--background-modifier-border-focus);
  transition:
    box-shadow 0.15s ease-in-out,
    border 0.15s ease-in-out;
}

/* .obsidian-default-textarea::placeholder {
  color: var(--text-faint);
} */
.obsidian-default-textarea {
  line-height: var(--line-height-tight);
}

.smtcmp-chat-user-input-container {
  position: relative;
  display: flex;
  flex-direction: column;
  -webkit-app-region: no-drag;
  background: var(--background-modifier-form-field);
  border: var(--input-border-width) solid var(--background-modifier-border);
  color: var(--text-normal);
  font-family: inherit;
  padding: calc(var(--size-2-3) + 1px);
  font-size: var(--font-ui-small);
  border-radius: var(--radius-s);
  outline: none;

  &:focus-within,
  &:focus,
  &:focus-visible,
  &:active {
    box-shadow: 0 0 0 2px var(--background-modifier-border-focus);
    transition: box-shadow 0.15s ease-in-out;
  }
}

.smtcmp-chat-user-input-files {
  display: flex;
  flex-direction: row;
  gap: var(--size-4-1);
  flex-wrap: wrap;
  padding-bottom: var(--size-4-1);
}

.smtcmp-chat-user-input-controls {
  display: flex;
  flex-direction: row;
  gap: var(--size-4-1);
  justify-content: space-between;
  align-items: center;
  height: var(--size-4-4);

  .smtcmp-chat-user-input-controls__model-select-container {
    flex-shrink: 1;
    overflow: hidden;
  }

  .smtcmp-chat-user-input-controls__buttons {
    flex-shrink: 0;
    display: flex;
    gap: var(--size-4-2);
    align-items: center;
  }
}

.smtcmp-chat-user-input-controls .smtcmp-chat-user-input-submit-button {
  display: flex;
  align-items: center;
  gap: var(--size-4-1);
  font-size: var(--font-smallest);
  color: var(--text-muted);
  background-color: transparent;
  border: none;
  box-shadow: none;
  padding: 0 var(--size-2-1);
  border-radius: var(--radius-s);
  height: var(--size-4-4);
  cursor: pointer;
  transition: color 0.15s ease-in-out;

  &:hover {
    color: var(--text-normal);
  }

  .smtcmp-chat-user-input-submit-button-icons {
    display: flex;
    align-items: center;
  }
}

.smtcmp-chat-user-input-file-badge {
  display: flex;
  align-items: center;
  background-color: var(--background-secondary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
  font-size: var(--font-smallest);
  padding: var(--size-2-1) var(--size-4-1);
  gap: var(--size-2-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;

  &.smtcmp-chat-user-input-file-badge-focused {
    border: 1px solid var(--interactive-accent);
  }

  .smtcmp-excluded-content {
    text-decoration: line-through;
    font-style: italic;
  }

  svg {
    flex-shrink: 0;
  }
}

.smtcmp-chat-user-input-file-badge:hover {
  background-color: var(--background-modifier-hover);
}

.smtcmp-chat-user-input-file-badge-delete {
  height: 100%;
  display: flex;
  align-items: center;
  color: var(--text-muted);
}

.smtcmp-chat-user-input-file-badge-eye {
  height: 100%;
  display: flex;
  align-items: center;
  margin: 0 var(--size-2-1) 0 var(--size-4-1);
}

.smtcmp-chat-user-input-file-badge-name {
  display: flex;
  flex-direction: row;
  gap: var(--size-2-1);
  flex-grow: 1;
  overflow: hidden;
  align-items: center;
}

.smtcmp-chat-user-input-file-badge-name-icon {
  color: var(--text-muted);
}

.smtcmp-chat-user-input-file-badge-name span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.smtcmp-chat-user-input-file-badge-name-suffix {
  color: var(--text-faint);
  flex-grow: 0;
}

.smtcmp-chat-user-input-file-badge-current {
  color: var(--color-base-50);
}

.smtcmp-chat-user-input-file-content-preview {
  background-color: var(--background-secondary);
  border-radius: var(--radius-s);
  border: 1px solid var(--background-modifier-border);
  max-height: 350px;
  overflow-y: auto;
  padding: 0 var(--size-4-2);

  img {
    max-width: 100%;
    max-height: 350px;
  }
}

/**
 * ChatUserInput
 */

.smtcmp-lexical-content-editable-root .mention {
  background-color: var(--tag-background);
  color: var(--tag-color);
  padding: var(--size-2-1) calc(var(--size-2-1));
  border-radius: var(--radius-s);
  background-color: var(--tag-background);
  color: var(--tag-color);
  padding: 0 calc(var(--size-2-1));
  border-radius: var(--radius-s);
  word-break: break-all;
}

.smtcmp-lexical-content-editable-paragraph {
  margin: 0;
  line-height: 1.6;
}

.smtcmp-popover {
  z-index: 1000;
  background: var(--background-primary);
  box-shadow: var(--shadow-s);
  border-radius: var(--radius-m);
  border: 1px solid var(--background-modifier-border);
  overflow: hidden;
}

.smtcmp-popover ul {
  padding: 0;
  list-style: none;
  margin: 0;
  max-height: 200px;
  overflow-y: scroll;
}

.smtcmp-popover ul {
  padding: var(--size-4-1) 0;
}

.smtcmp-popover ul li {
  margin: 0;
  min-width: 180px;
  font-size: var(--font-ui-smaller);
  outline: none;
  cursor: pointer;
  border-radius: 0;
}

.smtcmp-popover ul li.selected {
  background: var(--background-modifier-hover);
}

.smtcmp-popover li {
  z-index: 1000;
  max-height: 300px;
  overflow-y: auto;
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  margin: 0 8px 0 8px;
  padding: var(--size-2-3) var(--size-4-2);
  color: var(--text-normal);
  cursor: pointer;
  line-height: var(--line-height-tight);
  font-size: var(--font-ui-smaller);
  display: flex;
  align-content: center;
  flex-direction: row;
  flex-shrink: 0;
  background-color: var(--background-primary);
  border-radius: 8px;
  border: 0;
  min-height: 20px;
  align-items: center;
  gap: var(--size-4-1);
  align-items: start;
}

.smtcmp-popover li.active {
  display: flex;
  width: 20px;
  height: 20px;
  background-size: contain;
}

.smtcmp-popover li:hover {
  background-color: var(--background-modifier-hover);
}

.smtcmp-popover-item-icon {
  display: flex;
  user-select: none;
  line-height: 16px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  height: 14px;
  padding-top: 1px;
  align-items: center;
  color: var(--text-muted);
  min-width: fit-content;
}

.smtcmp-popover li:hover {
  background-color: var(--background-modifier-hover);
  cursor: pointer;
}
.smtcmp-popover li .smtcmp-chat-list-dropdown-item-icon {
  visibility: hidden;
  padding: var(--size-2-1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
}
.smtcmp-popover li:hover .smtcmp-chat-list-dropdown-item-icon {
  visibility: visible;
}
.smtcmp-popover li .smtcmp-chat-list-dropdown-item-icon:hover {
  background-color: var(--background-modifier-hover);
  border-radius: var(--radius-s);
}

.smtcmp-chat-list-dropdown-empty {
  background: transparent;
  cursor: default;
  color: var(--text-faint);
}

.smtcmp-chat-list-dropdown-content {
  width: 280px;
  max-width: 280px;
}

.smtcmp-chat-list-dropdown-content li {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.smtcmp-chat-list-dropdown-item-title {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

input[type='text'].smtcmp-chat-list-dropdown-item-title-input {
  width: 100%;
  font-size: var(--font-ui-smaller);
}

.smtcmp-chat-list-dropdown-item-actions {
  display: flex;
  align-items: center;
  gap: var(--size-4-1);
}

.smtcmp-code-block {
  position: relative;
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
}

.smtcmp-code-block code {
  padding: 0;
}

.smtcmp-code-block-header {
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-smallest);
  display: flex;
  border-bottom: 1px solid var(--background-modifier-border);
  background-color: var(--background-primary);
  border-radius: var(--radius-s) var(--radius-s) 0 0;
  height: calc(var(--size-4-8) - var(--size-4-1));
}

.smtcmp-code-block-header-filename {
  padding-left: var(--size-4-2);
  font-size: var(--font-medium);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }
}

.smtcmp-code-block-header-button {
  margin-left: auto;
  min-width: fit-content;
  height: 100%;
  display: flex;
  gap: 0;
  overflow: hidden;
}

.smtcmp-code-block-header-button button {
  height: 100%;
  padding: 0 var(--size-4-2);
  display: flex;
  gap: var(--size-4-1);
  box-shadow: none;
  border: 0;
  border-radius: 0;
  font-size: var(--font-small);
  background-color: inherit;
  cursor: pointer;

  &:hover {
    background-color: var(--background-modifier-hover);
  }
}

.smtcmp-code-block-content {
  margin: 0;
}

.smtcmp-code-block-obsidian-markdown {
  padding: var(--size-4-3);
}

#smtcmp-apply-view {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
  font-family: var(--font-interface);

  --smtcmp-current-color-rgb: 185, 28, 28; /* red-700 */
  --smtcmp-incoming-color-rgb: 4, 120, 87; /* emerald-700 */

  .view-content {
    padding: 0;
  }

  .markdown-source-view.mod-cm6 {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .cm-editor {
    flex: 1 1 0;
    min-height: 0;
    position: relative !important;
    box-sizing: border-box;
    display: flex !important;
    flex-direction: column;
  }

  .cm-scroller {
    padding: var(--file-margins);
    display: flex !important;
    align-items: flex-start !important;
    line-height: 1.4;
    height: 100%;
    overflow-x: auto;
    position: relative;
    z-index: 0;
  }

  .cm-sizer {
    max-width: var(--file-line-width);
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    padding-bottom: 488px;
  }

  .view-header {
    height: var(--header-height);
    display: flex;
    border-bottom: var(--file-header-border);
    background-color: var(--background-primary);
    z-index: 1;
    position: relative;
    gap: var(--size-4-2);
    padding: 0 var(--size-4-3);
  }

  .view-header-title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
  }

  .view-actions {
    button {
      color: var(--text-normal);
      font-weight: var(--font-medium);
      gap: 2px;
    }
  }

  .smtcmp-diff-block {
    white-space: pre-wrap;
    word-break: break-word;
    display: flex;
    align-items: start;
    gap: var(--size-4-1);
    color: var(--text-normal);
    line-height: var(--line-height-normal);
  }

  .smtcmp-diff-block.added {
    background-color: rgba(var(--smtcmp-incoming-color-rgb), 0.3);
  }

  .smtcmp-diff-block.removed {
    background-color: rgba(var(--smtcmp-current-color-rgb), 0.3);
  }

  .smtcmp-diff-block-container {
    position: relative;
    scroll-margin-top: var(--size-4-5);
  }

  .smtcmp-diff-block-actions {
    position: absolute;
    right: 0;
    top: 0;
    transform: translateY(-100%);
    display: flex;
    gap: 0;
    border-radius: var(--radius-s) var(--radius-s) 0 0;
    overflow: hidden;

    button {
      padding: var(--size-4-1) var(--size-4-2);
      font-size: var(--font-ui-smaller);
      border-radius: 0;
      height: fit-content;
    }
  }

  .smtcmp-accept {
    color: white;
    background: rgba(var(--smtcmp-incoming-color-rgb), 0.8);
    &:hover {
      background: rgba(var(--smtcmp-incoming-color-rgb), 1);
    }
  }

  .smtcmp-exclude {
    color: white;
    background: rgba(var(--smtcmp-current-color-rgb), 0.8);
    &:hover {
      background: rgba(var(--smtcmp-current-color-rgb), 1);
    }
  }

  .smtcmp-diff-navigation {
    display: flex;
    align-items: center;
  }
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

button.smtcmp-chat-input-model-select {
  background-color: transparent;
  box-shadow: none;
  border: 0;
  padding: 0;
  font-size: var(--font-smallest);
  font-weight: var(--font-medium);
  color: var(--text-muted);
  justify-content: flex-start;
  align-items: center;
  cursor: pointer;
  height: var(--size-4-4);
  max-width: 100%;

  &:hover {
    color: var(--text-normal);
  }

  .smtcmp-chat-input-model-select__model-name {
    flex-shrink: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .smtcmp-chat-input-model-select__icon {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.smtcmp-query-progress {
  font-size: var(--font-ui-smaller);
  color: var(--text-muted);
}

.smtcmp-query-progress-detail {
  font-size: var(--font-smallest);
  color: var(--text-faint);
}

.smtcmp-dot-loader {
  display: inline-block;
  text-align: left;
}

.smtcmp-dot-loader::after {
  content: '...';
  animation: dotFade 0.75s steps(4, end) infinite;
  color: var(--text-muted);
}

@keyframes dotFade {
  0%,
  100% {
    content: '';
  }
  25% {
    content: '.';
  }
  50% {
    content: '..';
  }
  75% {
    content: '...';
  }
}

.smtcmp-tooltip-content {
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
  padding: var(--size-4-1) var(--size-4-2);
  font-size: var(--font-smallest);
  animation: fadeIn 0.1s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.smtcmp-settings-textarea {
  display: block;
  border-top: none;
  padding: 0;

  .smtcmp-item-control {
    width: 100%;
  }

  .setting-item-control {
    padding-bottom: var(--size-4-3);
  }

  textarea {
    width: 100%;
    min-height: 100px;
    resize: none;
  }
}

/* prevent setting-item:first-child overwriting padding-top and border-top */
.smtcmp-settings-textarea-header {
  padding-top: 0.75em !important;
  border-top: 1px solid var(--background-modifier-border) !important;
}

.smtcmp-settings-model-container {
  margin: var(--size-4-2) 0;
  padding: var(--size-4-2) var(--size-4-4);
  border-left: 2px solid var(--interactive-accent);
  background-color: var(--background-secondary);
  border-radius: var(--radius-s);

  .setting-item {
    border-top: none;

    &:first-child {
      margin-top: var(--size-4-2);
    }
  }
}

.smtcmp-dialog-content {
  position: fixed;
  left: calc(50% - var(--size-4-4));
  top: 50%;
  z-index: 50;
  display: grid;
  width: calc(100% - var(--size-4-8));
  max-width: 32rem;
  transform: translate(-50%, -50%);
  gap: var(--size-4-2);
  border: var(--border-width) solid var(--background-modifier-border);
  background-color: var(--background-secondary);
  padding: var(--size-4-5);
  transition-duration: 200ms;
  border-radius: var(--radius-m);
  box-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  margin: var(--size-4-4);

  .smtcmp-dialog-header {
    margin-bottom: var(--size-4-2);
    display: grid;
    gap: var(--size-2-3);
  }

  .smtcmp-dialog-title {
    font-size: var(--font-ui-medium);
    font-weight: var(--font-semibold);
    line-height: var(--line-height-tight);
    margin: 0;
  }

  .smtcmp-dialog-input {
    display: grid;
    gap: var(--size-4-1);

    & label {
      font-size: var(--font-ui-smaller);
    }
  }

  .smtcmp-dialog-description {
    font-size: var(--font-ui-small);
    color: var(--text-muted);
    margin: 0;
  }

  .smtcmp-dialog-footer {
    margin-top: var(--size-4-2);
    display: flex;
    justify-content: flex-end;
  }

  .smtcmp-dialog-close {
    position: absolute;
    right: var(--size-4-4);
    top: var(--size-4-4);
    cursor: var(--cursor);
    opacity: 0.7;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }
}

.smtcmp-template-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--size-4-1);
  width: 100%;

  .smtcmp-template-menu-item-delete {
    display: flex;
    align-items: center;
    padding: var(--size-4-1);
    margin: calc(var(--size-4-1) * -1);
    opacity: 0.7;
    transition: opacity 0.2s;

    &:hover {
      opacity: 1;
    }
  }
}

.smtcmp-assistant-message-actions {
  display: flex;
  align-items: center;
  justify-content: end;

  button {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 20px;
    width: 20px;
    padding: 0;
    background-color: transparent;
    border-color: transparent;
    box-shadow: none;
    color: var(--text-faint);
    cursor: pointer;

    &:hover {
      background-color: var(--background-modifier-hover);
    }
  }

  .smtcmp-assistant-message-actions-icon--copied {
    color: var(--text-muted);
  }
}

.smtcmp-popover-content {
  z-index: 1000;
  background-color: var(--background-primary);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
  padding: var(--size-4-2);
  font-size: var(--font-smallest);
  animation: fadeIn 0.1s ease-in-out;
}

.smtcmp-similarity-search-results {
  display: flex;
  flex-direction: column;
  font-size: var(--font-smaller);
  padding-top: var(--size-4-1);
  padding-bottom: var(--size-4-1);
  user-select: none;

  .smtcmp-similarity-search-results__trigger {
    display: flex;
    align-items: center;
    gap: var(--size-4-1);
    padding: var(--size-4-1);
    border-radius: var(--radius-s);
    cursor: pointer;
    &:hover {
      background-color: var(--background-modifier-hover);
    }
  }

  .smtcmp-similarity-search-item {
    display: flex;
    align-items: center;
    justify-content: start;
    gap: var(--size-4-2);
    padding: var(--size-4-1);
    border-radius: var(--radius-s);
    cursor: pointer;

    &:hover {
      background-color: var(--background-modifier-hover);
    }

    .smtcmp-similarity-search-item__similarity {
      flex-shrink: 0;
      font-size: var(--font-smallest);
      color: var(--text-muted);
    }

    .smtcmp-similarity-search-item__path {
      flex-shrink: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-size: var(--font-smallest);
    }

    .smtcmp-similarity-search-item__line-numbers {
      flex-shrink: 0;
      margin-left: auto;
      font-size: var(--font-smallest);
    }
  }
}

.smtcmp-llm-info-content {
  width: 320px;
  display: grid;
  gap: var(--size-4-3);
}

.smtcmp-llm-info-header {
  display: grid;
  gap: var(--size-2-2);
  font-size: var(--font-ui-small);
  font-weight: var(--font-semibold);
}

.smtcmp-llm-info-tokens {
  display: grid;
  gap: var(--size-4-2);
}

.smtcmp-llm-info-tokens-header {
  font-size: var(--font-ui-small);
  font-weight: var(--font-medium);
}

.smtcmp-llm-info-tokens-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: var(--size-4-5);
  row-gap: var(--size-4-2);
  font-size: var(--font-ui-small);
}

.smtcmp-llm-info-token-row {
  display: flex;
  align-items: center;
  gap: var(--size-2-3);
}

.smtcmp-llm-info-token-value {
  margin-left: auto;
  color: var(--text-muted);
}

.smtcmp-llm-info-token-total {
  grid-column: span 2;
  font-weight: var(--font-medium);
}

.smtcmp-llm-info-footer-row {
  display: flex;
  align-items: center;
  gap: var(--size-2-3);
  font-size: var(--font-ui-small);
  font-weight: var(--font-medium);
}

.smtcmp-llm-info-footer-value {
  margin-left: auto;
}

.smtcmp-llm-info-model {
  color: var(--text-muted);
}

.smtcmp-llm-info-icon--input {
  height: var(--size-4-3);
  width: var(--size-4-3);
  color: var(--color-green);
}

.smtcmp-llm-info-icon--output {
  height: var(--size-4-3);
  width: var(--size-4-3);
  color: var(--color-blue);
}

.smtcmp-llm-info-icon--total {
  height: var(--size-4-3);
  width: var(--size-4-3);
  color: var(--text-normal);
}

.smtcmp-llm-info-icon--footer {
  height: var(--size-4-4);
  width: var(--size-4-4);
}

/* Settings */

.smtcmp-settings-support-smart-composer {
  border-top: none;
}

.smtcmp-settings-section:not(:first-child) {
  margin-top: var(--size-4-10);
}

.smtcmp-settings-header {
  color: var(--text-normal);
  font-size: var(--h1-size);
  line-height: var(--line-height-tight);
  font-weight: var(--h1-weight);
  margin: var(--size-4-2) 0;
}

.smtcmp-settings-sub-header {
  color: var(--text-normal);
  font-size: var(--h4-size);
  line-height: var(--line-height-tight);
  font-weight: var(--h4-weight);
  margin: var(--size-4-3) 0;
}

.smtcmp-settings-desc {
  color: var(--text-muted);
  font-size: var(--font-ui-small);
  line-height: var(--line-height-tight);
  margin: var(--size-4-1) 0;
}

.smtcmp-settings-required::after {
  color: var(--color-red);
  content: '*';
  display: inline-block;
  font-size: var(--font-ui-medium);
  font-weight: var(--font-bold);
  margin-left: var(--size-4-1);
}

/* Settings: Embedding DB Manage */

.smtcmp-settings-embedding-db-manage-root {
  padding: var(--size-4-2);
}

.smtcmp-settings-embedding-db-manage-header {
  display: flex;
  align-items: center;
  gap: var(--size-4-2);
  font-size: var(--font-ui-small);
}

.smtcmp-settings-embedding-db-manage-table {
  width: 100%;
  border-collapse: collapse;
}

.smtcmp-settings-embedding-db-manage-table tr {
  border-bottom: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-settings-embedding-db-manage-table th,
.smtcmp-settings-embedding-db-manage-table td {
  padding: var(--size-4-2);
  text-align: left;
}

.smtcmp-settings-embedding-db-manage-table th {
  font-weight: var(--font-medium);
  color: var(--text-muted);
  padding-bottom: var(--size-4-3);
}

.smtcmp-settings-embedding-db-manage-table td {
  padding-top: var(--size-4-3);
  padding-bottom: var(--size-4-3);
  vertical-align: middle;
}

.smtcmp-settings-embedding-db-manage-actions {
  display: flex;
  gap: var(--size-4-2);
}

.smtcmp-settings-embedding-db-manage-actions-loading {
  display: flex;
  align-items: center;
  gap: var(--size-2-2);
  font-size: var(--font-ui-smaller);
}

/* Settings: tables */

.smtcmp-settings-table-container {
  overflow-x: auto;
}

.smtcmp-settings-table {
  margin: var(--size-4-3) 0;
  width: 100%;
  border-collapse: collapse;
}

.smtcmp-settings-table th {
  padding: var(--size-4-1) var(--size-4-1) var(--size-4-2) var(--size-4-1);
  text-align: left;
  vertical-align: middle;
  font-weight: var(--font-medium);
  color: var(--text-muted);
}

.smtcmp-settings-table tbody td {
  height: var(--size-4-10);
  padding: var(--size-4-1);
  text-align: left;
  vertical-align: middle;
}

.smtcmp-settings-table tfoot td {
  padding: var(--size-4-1);
  text-align: right;
  vertical-align: middle;
}

.smtcmp-settings-table thead tr {
  border-bottom: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-settings-table tbody tr {
  border-bottom: var(--border-width) solid var(--background-modifier-border);
}

.smtcmp-settings-actions {
  display: flex;
  align-items: center;
  gap: var(--size-4-2);
}

.smtcmp-settings-actions button {
  padding: var(--size-4-1) var(--size-4-2);
}

.smtcmp-settings-table-api-key {
  cursor: pointer;
  color: var(--text-muted);
  font-size: var(--font-ui-small);
}

.smtcmp-settings-table-api-key:hover {
  text-decoration: underline;
}

.smtcmp-error-modal {
  min-width: 60vw;
}

.smtcmp-error-modal-content {
  max-height: 50vh;
  display: flex;
  flex-direction: column;
  user-select: text;
}

.smtcmp-error-modal-message {
  white-space: pre-line;
}

.smtcmp-error-modal-log {
  white-space: pre-wrap;
  word-break: break-word;
  margin-top: 1rem;
  flex-grow: 1;
  overflow-y: auto;
  user-select: text;
  cursor: text;
  font-size: var(--font-ui-small);
}

.smtcmp-error-modal-buttons {
  margin-top: 1rem;
}

.smtcmp-assistant-message-metadata {
  display: flex;
  flex-direction: column;
  margin-top: var(--size-4-1);
  border-left: 2px solid var(--background-modifier-border);
  padding-left: var(--size-4-1);
}

.smtcmp-assistant-message-metadata-content {
  color: var(--text-muted);
  padding-left: var(--size-4-1);
  font-size: var(--font-ui-small);
}

.smtcmp-assistant-message-metadata-annotations {
  display: flex;
  flex-direction: column;
  gap: var(--size-2-1);
}

.smtcmp-assistant-message-metadata-toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--size-4-1);
  font-size: var(--font-ui-small);
  color: var(--text-muted);
  background-color: transparent;
  cursor: pointer;
  border-radius: var(--radius-s);
  padding: var(--size-2-3) var(--size-4-1);
  user-select: none;

  &:hover {
    background-color: var(--background-modifier-hover);
  }
}

.smtcmp-assistant-message-metadata-toggle-icon {
  width: var(--size-4-4);
  height: var(--size-4-4);
}

/* Overrides default .markdown-rendered class styles from Obsidian */
.smtcmp-markdown-rendered {
  &.markdown-rendered {
    --render-scale: 1;
    font-size: calc(var(--render-scale) * 1rem);
    line-height: 1.5;

    &.smtcmp-scale-xs {
      --render-scale: 0.8;
    }

    &.smtcmp-scale-sm {
      --render-scale: 0.85;
    }

    &.smtcmp-scale-base {
      --render-scale: 1;
    }

    /* override default variables */
    --p-spacing: calc(var(--render-scale) * 1rem);
    --heading-spacing: calc(var(--p-spacing) * 2.5);
    --checkbox-size: calc(var(--render-scale) * 1rem);
    --checkbox-radius: calc(var(--render-scale) * var(--radius-s));

    /* adjust list indent */
    --list-indent: 1.5em;

    blockquote {
      padding-inline-start: calc(var(--render-scale) * 1.5rem);
    }

    pre {
      padding: calc(var(--render-scale) * 0.75rem)
        calc(var(--render-scale) * 1rem);
    }

    hr {
      margin: calc(var(--render-scale) * 2rem) 0;
    }

    th,
    td {
      font-size: calc(var(--render-scale) * 1rem);
      padding: calc(var(--render-scale) * 0.25rem)
        calc(var(--render-scale) * 0.5rem);
    }

    .callout {
      padding: calc(var(--render-scale) * 0.75rem)
        calc(var(--render-scale) * 0.75rem) calc(var(--render-scale) * 0.75rem)
        calc(var(--render-scale) * 1.5rem);
    }

    .callout-icon {
      height: calc(var(--render-scale) * 1rem);
      width: calc(var(--render-scale) * 1rem);
    }
  }

  /* Show frontmatter which is hidden by default */
  .frontmatter {
    display: block !important;
    background-color: transparent;
    border-top: 1px solid var(--background-modifier-border);
    border-bottom: 1px solid var(--background-modifier-border);
  }

  /* Hide copy code buttons */
  .copy-code-button {
    display: none !important;
  }
}

.smtcmp-mention-popover {
  width: 360px;
  max-width: 360px;
}

.smtcmp-mention-popover-folder-path {
  margin-left: auto;
  padding-left: var(--size-4-2);
  flex-shrink: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  direction: rtl;
  white-space: nowrap;
  color: var(--text-muted);
}
