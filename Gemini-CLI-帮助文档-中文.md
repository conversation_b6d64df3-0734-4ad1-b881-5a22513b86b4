### Gemini CLI 帮助文档 (中文翻译)

#### 基础用法:
*   ==**添加上下文**: 使用 `@` 来指定文件作为上下文 (例如, `@src/myFile.ts`)，从而定位到特定的文件或文件夹。==
*   **Shell 模式**: 通过 `!` 来执行 shell 命令 (例如, `!npm run start`) 或使用自然语言 (例如, "启动服务器")。

---

#### 命令:
*   `/help` - 获取 gemini-cli 的帮助信息
*   `/docs` - 在浏览器中打开完整的 Gemini CLI 文档
*   `/clear` - 清空屏幕和对话历史
*   `/theme` - 更改主题
*   `/auth` - 更改认证方式
*   `/editor` - 设置外部编辑器偏好
*   `/privacy` - 显示隐私声明
*   `/stats` - 查看会话统计信息
*   ==`/mcp` - 列出已配置的 MCP 服务器和工具==
*   `/memory` - 管理记忆。用法: `/memory <show|refresh|add> [要添加的文本]`
*   ==`/tools` - 列出可用的 Gemini CLI 工具==
*   `/about` - 显示版本信息
*   `/bug` - 提交错误报告
*   ==`/chat` - 管理对话历史。用法: `/chat <list|save|resume> [标签]`==
*   `/quit` - 退出 CLI
*   `/compress` - 通过用摘要替换上下文来压缩上下文。
*   `!` - 执行 shell 命令

---

#### 键盘快捷键:
*   `Enter` - 发送消息
*   `Shift+Enter` - 换行
*   `Up/Down` (上/下箭头) - 在你的输入历史中循环
*   `Alt+Left/Right` (Alt+左/右箭头) - 在输入内容中按单词跳转
*   `Esc` - 取消操作
*   `Ctrl+C` - 退出应用程序
